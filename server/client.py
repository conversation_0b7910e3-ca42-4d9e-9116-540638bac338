import time
import requests
import numpy as np
import  SimpleITK as sitk
import io
from PIL import Image
import argparse

API_URL = "http://127.0.0.1:8080/predict"


def extract_nii_slices(nii_path, slice_mode='middle', axis='z'):
    """
    从nii.gz文件提取切片的通用函数

    Args:
        nii_path (str): nii.gz文件路径
        slice_mode (str): 切片模式，'middle' 或 'multi'
        axis (str): 当slice_mode='multi'时指定轴向
    Returns:
        PIL Image: 如果stack_channels=True，返回3通道RGB图像
    """
    # 读取医学影像
    image = sitk.ReadImage(nii_path)
    arr = sitk.GetArrayFromImage(image)

    if slice_mode == 'middle':
        return _extract_middle_slices_stacked(arr)
    elif slice_mode == 'multi':
        return _extract_multi_slices_stacked(arr, axis)
    else:
        raise ValueError(f"不支持的切片模式: {slice_mode}")
       

def _extract_middle_slices_stacked(arr):
    """提取xyz面的中间切片并堆叠成3通道RGB图像"""
    depth, height, width = arr.shape

    # Z轴中间切片 (轴向面)
    z_middle = depth // 2
    axial_slice = arr[z_middle, :, :]

    # Y轴中间切片 (冠状面)
    y_middle = height // 2
    coronal_slice = arr[:, y_middle, :]

    # X轴中间切片 (矢状面)
    x_middle = width // 2
    sagittal_slice = arr[:, :, x_middle]

    # 将三个切片堆叠成3通道图像
    return _stack_slices_to_rgb_util(axial_slice, coronal_slice, sagittal_slice)


def _extract_multi_slices_stacked(arr, axis):
    """提取指定轴的25%、50%、75%位置切片并堆叠成3通道RGB图像"""
    depth, height, width = arr.shape

    if axis == 'z':
        positions = [int(depth * 0.25), int(depth * 0.5), int(depth * 0.75)]
        slice1 = arr[min(positions[0], depth - 1), :, :]
        slice2 = arr[min(positions[1], depth - 1), :, :]
        slice3 = arr[min(positions[2], depth - 1), :, :]

    elif axis == 'y':
        positions = [int(height * 0.25), int(height * 0.5), int(height * 0.75)]
        slice1 = arr[:, min(positions[0], height - 1), :]
        slice2 = arr[:, min(positions[1], height - 1), :]
        slice3 = arr[:, min(positions[2], height - 1), :]

    elif axis == 'x':
        positions = [int(width * 0.25), int(width * 0.5), int(width * 0.75)]
        slice1 = arr[:, :, min(positions[0], width - 1)]
        slice2 = arr[:, :, min(positions[1], width - 1)]
        slice3 = arr[:, :, min(positions[2], width - 1)]
    else:
        raise ValueError(f"不支持的轴向: {axis}")

    # 将三个切片堆叠成3通道图像
    return _stack_slices_to_rgb_util(slice1, slice2, slice3)


def _normalize_slice_util(arr):
    """标准化单个切片到0-255范围"""
    arr_min, arr_max = arr.min(), arr.max()
    if arr_max > arr_min:
        return ((arr - arr_min) / (arr_max - arr_min) * 255).astype(np.uint8)
    else:
        return np.zeros_like(arr, dtype=np.uint8)


def _stack_slices_to_rgb_util(slice1, slice2, slice3):
    """将三个切片堆叠成RGB图像"""
    # 确保所有切片具有相同的尺寸
    target_shape = slice1.shape

    # 如果切片尺寸不同，需要调整到相同尺寸
    if slice2.shape != target_shape:
        slice2 = _resize_slice_util(slice2, target_shape)
    if slice3.shape != target_shape:
        slice3 = _resize_slice_util(slice3, target_shape)

    # 标准化每个切片
    slice1_norm = _normalize_slice_util(slice1)
    slice2_norm = _normalize_slice_util(slice2)
    slice3_norm = _normalize_slice_util(slice3)

    # 堆叠成RGB图像
    rgb_array = np.stack([slice1_norm, slice2_norm, slice3_norm], axis=-1)

    # 转换为PIL图像
    return Image.fromarray(rgb_array, mode='RGB')


def _resize_slice_util(slice_arr, target_shape):
    """调整切片尺寸"""
    try:
        from scipy import ndimage
        zoom_factors = [target_shape[i] / slice_arr.shape[i] for i in range(len(target_shape))]
        return ndimage.zoom(slice_arr, zoom_factors, order=1)
    except ImportError:
        # 如果没有scipy，使用简单的重复/截断方法
        return np.resize(slice_arr, target_shape)




def predict_nii(nii_pth, mode):
    # 2. 构建 multipart/form-data 请求
    print("\n--- 发送请求 (multipart/form-data) ---")
    try:
        img_obj = extract_nii_slices(nii_pth)
        array = np.array(img_obj)

        # 将NumPy数组序列化到内存
        buffer = io.BytesIO()
        np.save(buffer, array)
        buffer.seek(0)

        files = {
            'array_file': ('array_slice.npy', buffer.getvalue(), 'application/x-numpy'),
        }
        data = {
            'mode': mode
        }
        start_time = time.time()
        response = requests.post(API_URL, files=files, data=data, timeout=30)  # data=data
        response.raise_for_status()
        result = response.json()
        print(f"\n✅ 请求成功！, 耗时：{(time.time() -  start_time):.6f}秒")
        print("服务器返回结果:")
        print(result)
    except requests.exceptions.RequestException as e:
        print(f"\n❌ 请求失败: {e}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='nii predict demo')
    parser.add_argument('-f',required=True,  help='nii file path')
    parser.add_argument('-t', default='middle', help='pridict mode， middle or multi')
    args = parser.parse_args()
    predict_nii(args.f, args.t)